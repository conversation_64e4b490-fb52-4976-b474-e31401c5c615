<UserControl x:Class="AircraftVisualization.Controls.Panel3D"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AircraftVisualization.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="200" d:DesignWidth="300">
    <UserControl.Resources>
        <LinearGradientBrush x:Key="HeaderGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#FF663300" Offset="0"/>
            <GradientStop Color="#FF331100" Offset="1"/>
        </LinearGradientBrush>
    </UserControl.Resources>
    
    <Grid Margin="0,0,0,10">
        <!-- 添加阴影效果 -->
        <Border Background="#FF0A0A0A" Margin="3,3,0,0"/>
        
        <!-- 主面板 -->
        <Border Background="#FF111111" BorderBrush="#FF222222" BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- 面板标题区域 -->
                <Border Grid.Row="0" Background="{StaticResource HeaderGradient}">
                    <Grid>
                        <!-- 左侧三角形装饰 -->
                        <Polygon Points="0,0 15,15 0,30" Fill="#FFFF6600" HorizontalAlignment="Left"/>
                        
                        <!-- 右侧三角形装饰 -->
                        <Polygon x:Name="RightTriangle" Points="300,0 285,15 300,30" Fill="#FFFF6600" HorizontalAlignment="Right"/>
                        
                        <!-- 标题文本 -->
                        <TextBlock x:Name="HeaderText" Text="面板标题" Foreground="#FFEEEEEE" 
                                   FontSize="14" FontWeight="Bold" 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"
                                   Margin="0,5">
                            <TextBlock.Effect>
                                <DropShadowEffect ShadowDepth="1" Color="Black" Opacity="0.8"/>
                            </TextBlock.Effect>
                        </TextBlock>
                    </Grid>
                </Border>
                
                <!-- 面板内容 -->
                <Grid Grid.Row="1" Background="#FF0A0A0A">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock x:Name="SubHeaderText" Text="Sub Header" Foreground="#FFAAAAAA" 
                               FontSize="10" Margin="5,2"/>
                    
                    <ContentPresenter Grid.Row="1" x:Name="ContentArea" Margin="5"/>
                </Grid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
