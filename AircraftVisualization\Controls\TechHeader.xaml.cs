using System.Windows;
using System.Windows.Controls;

namespace AircraftVisualization.Controls
{
    /// <summary>
    /// TechHeader.xaml 的交互逻辑
    /// </summary>
    public partial class TechHeader : UserControl
    {
        public TechHeader()
        {
            InitializeComponent();
        }

        // 标题文本属性
        public string HeaderTitle
        {
            get { return (string)GetValue(HeaderTitleProperty); }
            set { SetValue(HeaderTitleProperty, value); }
        }

        public static readonly DependencyProperty HeaderTitleProperty =
            DependencyProperty.Register("HeaderTitle", typeof(string), typeof(TechHeader), 
                new PropertyMetadata("军工机型信息可视化", OnHeaderTitleChanged));

        private static void OnHeaderTitleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            TechHeader header = d as TechHeader;
            if (header != null)
            {
                header.HeaderText.Text = e.NewValue as string;
            }
        }
    }
}
