using System.Windows;
using System.Windows.Controls;

namespace AircraftVisualization.Controls
{
    /// <summary>
    /// Panel3D.xaml 的交互逻辑
    /// </summary>
    public partial class Panel3D : UserControl
    {
        public Panel3D()
        {
            InitializeComponent();
            this.SizeChanged += Panel3D_SizeChanged;
        }

        private void Panel3D_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            // 更新右侧三角形的位置
            if (RightTriangle != null && ActualWidth > 0)
            {
                RightTriangle.Points.Clear();
                RightTriangle.Points.Add(new System.Windows.Point(ActualWidth, 0));
                RightTriangle.Points.Add(new System.Windows.Point(ActualWidth - 15, 15));
                RightTriangle.Points.Add(new System.Windows.Point(ActualWidth, 30));
            }
        }

        // 标题属性
        public string Header
        {
            get { return (string)GetValue(HeaderProperty); }
            set { SetValue(HeaderProperty, value); }
        }

        public static readonly DependencyProperty HeaderProperty =
            DependencyProperty.Register("Header", typeof(string), typeof(Panel3D), 
                new PropertyMetadata("面板标题", OnHeaderChanged));

        private static void OnHeaderChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            Panel3D panel = d as Panel3D;
            if (panel != null)
            {
                panel.HeaderText.Text = e.NewValue as string;
            }
        }

        // 子标题属性
        public string SubHeader
        {
            get { return (string)GetValue(SubHeaderProperty); }
            set { SetValue(SubHeaderProperty, value); }
        }

        public static readonly DependencyProperty SubHeaderProperty =
            DependencyProperty.Register("SubHeader", typeof(string), typeof(Panel3D), 
                new PropertyMetadata("Sub Header", OnSubHeaderChanged));

        private static void OnSubHeaderChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            Panel3D panel = d as Panel3D;
            if (panel != null)
            {
                panel.SubHeaderText.Text = e.NewValue as string;
            }
        }

        // 内容属性
        public object PanelContent
        {
            get { return GetValue(PanelContentProperty); }
            set { SetValue(PanelContentProperty, value); }
        }

        public static readonly DependencyProperty PanelContentProperty =
            DependencyProperty.Register("PanelContent", typeof(object), typeof(Panel3D), 
                new PropertyMetadata(null, OnPanelContentChanged));

        private static void OnPanelContentChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            Panel3D panel = d as Panel3D;
            if (panel != null)
            {
                panel.ContentArea.Content = e.NewValue;
            }
        }
    }
}
