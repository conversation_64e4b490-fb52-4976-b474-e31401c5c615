<UserControl x:Class="AircraftVisualization.Controls.TechHeader"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:AircraftVisualization.Controls"
             mc:Ignorable="d"
             d:DesignHeight="80" d:DesignWidth="800">
    <UserControl.Resources>
        <!-- 根据附图调整的标题背景渐变色 -->
        <LinearGradientBrush x:Key="HeaderGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#FF221A14" Offset="0"/>
            <GradientStop Color="#FF0A0805" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="LineGradient1" StartPoint="0,0" EndPoint="1,0">
            <GradientStop Color="#FF006600" Offset="0"/>
            <GradientStop Color="#FF00FF00" Offset="0.5"/>
            <GradientStop Color="#FF006600" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="LineGradient2" StartPoint="0,0" EndPoint="1,0">
            <GradientStop Color="#FF663300" Offset="0"/>
            <GradientStop Color="#FFFF6600" Offset="0.5"/>
            <GradientStop Color="#FF663300" Offset="1"/>
        </LinearGradientBrush>
    </UserControl.Resources>

    <Grid>
        <!-- 背景 -->
        <Border Background="{StaticResource HeaderGradient}" BorderThickness="0">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="2" Color="Black" Opacity="0.7" BlurRadius="5"/>
            </Border.Effect>
        </Border>

        <!-- 装饰线条 - 左侧 -->
        <Canvas HorizontalAlignment="Left" VerticalAlignment="Center" Margin="10,0,0,0">
            <!-- 水平线 - 绿色线条 -->
            <Line X1="0" Y1="-5" X2="150" Y2="-5" Stroke="{StaticResource LineGradient1}" StrokeThickness="1"/>
            <Line X1="0" Y1="-2" X2="180" Y2="-2" Stroke="{StaticResource LineGradient1}" StrokeThickness="1"/>
            <Line X1="0" Y1="1" X2="200" Y2="1" Stroke="{StaticResource LineGradient1}" StrokeThickness="1"/>

            <!-- 水平线 - 橙色线条 -->
            <Line X1="0" Y1="5" X2="220" Y2="5" Stroke="{StaticResource LineGradient2}" StrokeThickness="1"/>
            <Line X1="0" Y1="8" X2="190" Y2="8" Stroke="{StaticResource LineGradient2}" StrokeThickness="1"/>
            <Line X1="0" Y1="11" X2="160" Y2="11" Stroke="{StaticResource LineGradient2}" StrokeThickness="1"/>

            <!-- 装饰元素 -->
            <Polygon Points="230,3 240,8 230,13 225,8" Fill="#FF00FF00"/>
            <Rectangle Canvas.Left="250" Canvas.Top="7" Width="20" Height="2" Fill="#FFFF6600"/>
        </Canvas>

        <!-- 装饰线条 - 右侧 -->
        <Canvas HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,10,0">
            <!-- 水平线 - 绿色线条 -->
            <Line X1="0" Y1="-5" X2="-150" Y2="-5" Stroke="{StaticResource LineGradient1}" StrokeThickness="1"/>
            <Line X1="0" Y1="-2" X2="-180" Y2="-2" Stroke="{StaticResource LineGradient1}" StrokeThickness="1"/>
            <Line X1="0" Y1="1" X2="-200" Y2="1" Stroke="{StaticResource LineGradient1}" StrokeThickness="1"/>

            <!-- 水平线 - 橙色线条 -->
            <Line X1="0" Y1="5" X2="-220" Y2="5" Stroke="{StaticResource LineGradient2}" StrokeThickness="1"/>
            <Line X1="0" Y1="8" X2="-190" Y2="8" Stroke="{StaticResource LineGradient2}" StrokeThickness="1"/>
            <Line X1="0" Y1="11" X2="-160" Y2="11" Stroke="{StaticResource LineGradient2}" StrokeThickness="1"/>

            <!-- 装饰元素 -->
            <Polygon Points="-230,3 -240,8 -230,13 -225,8" Fill="#FF00FF00"/>
            <Rectangle Canvas.Left="-270" Canvas.Top="7" Width="20" Height="2" Fill="#FFFF6600"/>
        </Canvas>

        <!-- 标题文本 -->
        <TextBlock x:Name="HeaderText" Text="军工机型信息可视化"
                   HorizontalAlignment="Center" VerticalAlignment="Center"
                   Foreground="#FFEEEEEE" FontSize="24" FontWeight="Bold">
            <TextBlock.Effect>
                <DropShadowEffect ShadowDepth="1" Color="#FF00FF00" Opacity="0.7" BlurRadius="3"/>
            </TextBlock.Effect>
        </TextBlock>

        <!-- 底部指示器 -->
        <Canvas VerticalAlignment="Bottom" HorizontalAlignment="Center">
            <!-- 绿色线条 -->
            <Rectangle Canvas.Left="-300" Canvas.Top="0" Width="600" Height="1" Fill="{StaticResource LineGradient1}"/>

            <!-- 中央指示器 -->
            <Rectangle Canvas.Left="-5" Canvas.Top="1" Width="10" Height="3" Fill="#FF00FF00">
                <Rectangle.Effect>
                    <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.5" BlurRadius="3"/>
                </Rectangle.Effect>
            </Rectangle>

            <!-- 左侧指示器 -->
            <Rectangle Canvas.Left="-200" Canvas.Top="1" Width="3" Height="3" Fill="#FF00FF00"/>
            <Rectangle Canvas.Left="-150" Canvas.Top="1" Width="3" Height="3" Fill="#FF00FF00"/>
            <Rectangle Canvas.Left="-100" Canvas.Top="1" Width="3" Height="3" Fill="#FF00FF00"/>
            <Rectangle Canvas.Left="-50" Canvas.Top="1" Width="3" Height="3" Fill="#FF00FF00"/>

            <!-- 右侧指示器 -->
            <Rectangle Canvas.Left="50" Canvas.Top="1" Width="3" Height="3" Fill="#FF00FF00"/>
            <Rectangle Canvas.Left="100" Canvas.Top="1" Width="3" Height="3" Fill="#FF00FF00"/>
            <Rectangle Canvas.Left="150" Canvas.Top="1" Width="3" Height="3" Fill="#FF00FF00"/>
            <Rectangle Canvas.Left="200" Canvas.Top="1" Width="3" Height="3" Fill="#FF00FF00"/>
        </Canvas>
    </Grid>
</UserControl>
