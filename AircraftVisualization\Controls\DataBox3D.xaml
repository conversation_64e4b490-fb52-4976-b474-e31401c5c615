<UserControl x:Class="AircraftVisualization.Controls.DataBox3D"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AircraftVisualization.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="80" d:DesignWidth="80">
    <Border Margin="2" Background="#FF050505">
        <Border BorderThickness="1">
            <Border.BorderBrush>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#FF00FF00" Offset="0"/>
                    <GradientStop Color="#FF003300" Offset="1"/>
                </LinearGradientBrush>
            </Border.BorderBrush>
            <Border.Effect>
                <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.3" BlurRadius="10"/>
            </Border.Effect>
            <Grid>
                <Rectangle>
                    <Rectangle.Fill>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#FF001100" Offset="0"/>
                            <GradientStop Color="#FF002200" Offset="1"/>
                        </LinearGradientBrush>
                    </Rectangle.Fill>
                </Rectangle>
                <StackPanel>
                    <TextBlock x:Name="TitleText" Text="数据标题" Foreground="#FFAAAAAA" 
                               FontSize="10" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                    <TextBlock x:Name="ValueText" Text="0" Foreground="#FF00FF00" 
                               FontSize="24" HorizontalAlignment="Center" FontWeight="Bold">
                        <TextBlock.Effect>
                            <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.5" BlurRadius="5"/>
                        </TextBlock.Effect>
                    </TextBlock>
                    <TextBlock x:Name="UnitText" Text="/单位" Foreground="#FFAAAAAA" 
                               FontSize="10" HorizontalAlignment="Center" Margin="0,0,0,2"/>
                </StackPanel>
            </Grid>
        </Border>
    </Border>
</UserControl>
