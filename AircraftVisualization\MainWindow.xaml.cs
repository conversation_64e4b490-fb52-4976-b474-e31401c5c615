﻿using System;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Linq;
using System.Collections.Generic;
using System.Windows.Threading;
using System.Windows.Media.Animation;

namespace AircraftVisualization;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    // 电场波形动态更新相关字段
    private DispatcherTimer waveformUpdateTimer;
    private Random random = new Random();
    private List<Point> baseWaveformPoints;
    private double updateCounter = 0;
    private double peakValue = 25.8;
    private double voltageValue = 800;
    private double riseTimeValue = 50;

    public MainWindow()
    {
        InitializeComponent();
        
        // 添加未处理异常捕获
        AppDomain.CurrentDomain.UnhandledException += (s, e) => 
        {
            MessageBox.Show($"应用程序发生未处理异常: {e.ExceptionObject}", 
                          "应用程序错误", 
                          MessageBoxButton.OK, 
                          MessageBoxImage.Error);
        };
        
        // 添加视频相关异常处理
        videoPlayer.MediaFailed += VideoPlayer_MediaFailed;
        
        // 添加窗口加载完成事件
        this.Loaded += MainWindow_Loaded;
    }
    
    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        // 窗口加载完成后加载视频
        LoadVideo();
        
        // 初始化电场波形动态更新
        InitializeWaveformAnimation();
    }
    
    private void LoadVideo()
    {
        try
        {
            // 使用正确的Resources目录（首字母大写）
            var videoFilePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources\\充电流程.mp4");
            
            if (!System.IO.File.Exists(videoFilePath))
            {
                // 检查是否有备用视频在resource目录（小写）
                var alternativeFilePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "resource\\充电流程.mp4");
                if (System.IO.File.Exists(alternativeFilePath))
                {
                    // 如果在小写目录找到了文件，使用这个文件
                    videoFilePath = alternativeFilePath;
                }
                else
                {
                    // 两个目录都没有文件，显示提示信息
                    MessageBox.Show("未找到视频文件：充电流程.mp4\n\n" +
                                  "请将视频文件放置在以下路径：\n" +
                                  $"{videoFilePath}", 
                                  "视频文件缺失", 
                                  MessageBoxButton.OK, 
                                  MessageBoxImage.Information);
                    return;
                }
            }
            
            try
            {
                // 设置视频源并播放
                videoPlayer.Source = new Uri(videoFilePath);
                videoPlayer.Play();
            }
            catch (Exception)
            {
                // 使用XAML中指定的路径尝试播放
                try
                {
                    videoPlayer.Stop();
                    videoPlayer.Play();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"视频播放错误: {ex.Message}\n\n" +
                                   "请确保视频文件格式正确且可被Windows Media Player播放。", 
                                   "视频播放错误", 
                                   MessageBoxButton.OK, 
                                   MessageBoxImage.Warning);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"视频加载错误: {ex.Message}\n\n" +
                           "请确保视频文件格式正确且可被Windows Media Player播放。", 
                           "视频加载错误", 
                           MessageBoxButton.OK, 
                           MessageBoxImage.Warning);
        }
    }

    /// <summary>
    /// 视频播放结束事件处理 - 实现循环播放
    /// </summary>
    private void VideoPlayer_MediaEnded(object sender, RoutedEventArgs e)
    {
        try
        {
            // 重新开始播放视频，实现循环效果
            videoPlayer.Position = TimeSpan.Zero;
            videoPlayer.Play();
        }
        catch (Exception)
        {
            // 忽略循环播放时的错误提示，让视频继续播放
            try
            {
                videoPlayer.Stop();
                videoPlayer.Play();
            }
            catch
            {
                // 如果再次失败，忽略异常，防止应用崩溃
            }
        }
    }
    
    /// <summary>
    /// 视频加载失败事件处理
    /// </summary>
    private void VideoPlayer_MediaFailed(object sender, ExceptionRoutedEventArgs e)
    {
        MessageBox.Show($"视频加载或播放失败: {e.ErrorException.Message}\n\n" +
                       "请确保视频文件格式正确且可被Windows Media Player播放。", 
                       "视频播放错误", 
                       MessageBoxButton.OK, 
                       MessageBoxImage.Warning);
    }

    /// <summary>
    /// 标题栏拖动事件处理
    /// </summary>
    private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ClickCount == 2)
        {
            ToggleMaximizeState();
        }
        else
        {
            this.DragMove();
        }
    }

    /// <summary>
    /// 最小化按钮点击事件
    /// </summary>
    private void BtnMinimize_Click(object sender, RoutedEventArgs e)
    {
        this.WindowState = WindowState.Minimized;
    }

    /// <summary>
    /// 最大化/还原按钮点击事件
    /// </summary>
    private void BtnMaximize_Click(object sender, RoutedEventArgs e)
    {
        ToggleMaximizeState();
    }

    /// <summary>
    /// 关闭按钮点击事件
    /// </summary>
    private void BtnClose_Click(object sender, RoutedEventArgs e)
    {
        this.Close();
    }

    /// <summary>
    /// 切换窗口最大化状态
    /// </summary>
    private void ToggleMaximizeState()
    {
        if (this.WindowState == WindowState.Maximized)
        {
            this.WindowState = WindowState.Normal;
            MaximizeIcon.Text = "\uE922"; // 最大化图标
        }
        else
        {
            this.WindowState = WindowState.Maximized;
            MaximizeIcon.Text = "\uE923"; // 还原图标
        }
    }
    
    /// <summary>
    /// 波形选择事件处理
    /// </summary>
    private void WaveformRadioButton_Checked(object sender, RoutedEventArgs e)
    {
        if (!(sender is RadioButton radioButton))
            return;
            
        try
        {
            // 获取关键波形显示面板中的Canvas
            var panel3D = LogicalTreeHelper.FindLogicalNode(this, "waveformPanel") as Controls.Panel3D;
            if (panel3D == null)
            {
                // 通过遍历查找Panel3D
                panel3D = FindVisualChildren<Controls.Panel3D>(this)
                    .FirstOrDefault(p => p.Header != null && p.Header.ToString() == "关键波形显示");
            }
            
            if (panel3D == null)
                return;
                
            // 查找Canvas元素
            var canvas = FindVisualChildren<Canvas>(panel3D).FirstOrDefault();
            if (canvas == null)
                return;
                
            // 隐藏所有波形元素
            foreach (var child in canvas.Children)
            {
                if (child is UIElement element)
                {
                    element.Visibility = Visibility.Collapsed;
                }
            }
            
            // 查找参数显示区域
            var gridRows = FindVisualChildren<Grid>(panel3D)
                .Where(g => g.Tag != null)
                .ToList();
                
            // 隐藏所有参数
            foreach (var grid in gridRows)
            {
                grid.Visibility = Visibility.Collapsed;
            }
            
            // 根据选择的波形类型显示对应的元素
            string selectedType = radioButton.Tag as string;
            if (string.IsNullOrEmpty(selectedType))
                return;
                
            // 显示对应的波形
            foreach (var child in canvas.Children)
            {
                if (child is UIElement element && child is FrameworkElement fe)
                {
                    string tag = fe.Tag as string;
                    if (!string.IsNullOrEmpty(tag) && tag.ToLower().Contains(selectedType.ToLower()))
                    {
                        element.Visibility = Visibility.Visible;
                    }
                }
            }
            
            // 显示对应的参数
            foreach (var grid in gridRows)
            {
                string tag = grid.Tag as string;
                if (!string.IsNullOrEmpty(tag) && tag.ToLower().Contains(selectedType.ToLower()))
                {
                    grid.Visibility = Visibility.Visible;
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"切换波形显示时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
    
    /// <summary>
    /// 查找特定类型的视觉子元素
    /// </summary>
    private IEnumerable<T> FindVisualChildren<T>(DependencyObject parent) where T : DependencyObject
    {
        int childCount = VisualTreeHelper.GetChildrenCount(parent);
        for (int i = 0; i < childCount; i++)
        {
            var child = VisualTreeHelper.GetChild(parent, i);
            
            if (child is T childOfType)
                yield return childOfType;
                
            foreach (var grandChild in FindVisualChildren<T>(child))
                yield return grandChild;
        }
    }

    /// <summary>
    /// 波形更新定时器的Tick事件处理
    /// </summary>
    private void WaveformUpdateTimer_Tick(object sender, EventArgs e)
    {
        try
        {
            // 更新计数器
            updateCounter += 0.1;
            
            // 更新电场波形
            UpdateElectricFieldWaveform();
            
            // 更新测量数据
            UpdateMeasurementData();
            
            // 更新控制参数
            UpdateControlParameters();
        }
        catch (Exception ex)
        {
            // 捕获任何可能的异常以避免应用程序崩溃
            System.Diagnostics.Debug.WriteLine("波形更新发生错误: " + ex.Message);
        }
    }
    
    /// <summary>
    /// 初始化波形动画
    /// </summary>
    private void InitializeWaveformAnimation()
    {
        try
        {
            // 查找电场测量面板
            var panel3D = FindVisualChildren<Controls.Panel3D>(this)
                .FirstOrDefault(p => p.Header != null && p.Header.ToString() == "电场测量");
                
            if (panel3D == null)
            {
                MessageBox.Show("未找到电场测量面板", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            
            // 查找电场波形Canvas和Polyline
            var canvas = FindVisualChildren<Canvas>(panel3D)
                .FirstOrDefault(c => c.Tag != null && c.Tag.ToString() == "efMeasurementCanvas");
                
            if (canvas == null)
            {
                MessageBox.Show("未找到电场测量Canvas", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            
            var waveform = FindVisualChildren<Polyline>(canvas)
                .FirstOrDefault(p => p.Tag != null && p.Tag.ToString() == "electricFieldWaveform");
                
            if (waveform == null)
            {
                MessageBox.Show("未找到电场波形Polyline", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            
            // 保存原始波形点集合
            baseWaveformPoints = new List<Point>();
            foreach (Point point in waveform.Points)
            {
                baseWaveformPoints.Add(point);
            }
            
            // 设置并启动定时器
            waveformUpdateTimer = new DispatcherTimer();
            waveformUpdateTimer.Interval = TimeSpan.FromMilliseconds(100); // 10Hz更新频率
            waveformUpdateTimer.Tick += WaveformUpdateTimer_Tick;
            waveformUpdateTimer.Start();
        }
        catch (Exception ex)
        {
            MessageBox.Show("初始化波形动画时发生错误: " + ex.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 更新电场波形数据
    /// </summary>
    private void UpdateElectricFieldWaveform()
    {
        try
        {
            // 查找电场测量面板
            var panel3D = FindVisualChildren<Controls.Panel3D>(this)
                .FirstOrDefault(p => p.Header != null && p.Header.ToString() == "电场测量");
                
            if (panel3D == null)
                return;
                
            // 查找电场波形Canvas
            var canvas = FindVisualChildren<Canvas>(panel3D)
                .FirstOrDefault(c => c.Tag != null && c.Tag.ToString() == "efMeasurementCanvas");
                
            if (canvas == null)
                return;
                
            // 查找需要的控件
            var waveform = FindVisualChildren<Polyline>(canvas).FirstOrDefault(p => p.Tag != null && p.Tag.ToString() == "electricFieldWaveform");
            var peakMarker = FindVisualChildren<Ellipse>(canvas).FirstOrDefault(e => e.Tag != null && e.Tag.ToString() == "peakPointMarker");
            var peakLine = FindVisualChildren<Line>(canvas).FirstOrDefault(l => l.Tag != null && l.Tag.ToString() == "peakPointLine");
            var peakValueText = FindVisualChildren<TextBlock>(canvas).FirstOrDefault(t => t.Tag != null && t.Tag.ToString() == "peakPointValue");
            var testPoint1 = FindVisualChildren<Ellipse>(canvas).FirstOrDefault(e => e.Tag != null && e.Tag.ToString() == "testPoint1");
            var testPoint2 = FindVisualChildren<Ellipse>(canvas).FirstOrDefault(e => e.Tag != null && e.Tag.ToString() == "testPoint2");
            var testPoint3 = FindVisualChildren<Ellipse>(canvas).FirstOrDefault(e => e.Tag != null && e.Tag.ToString() == "testPoint3");
            
            if (waveform == null || peakMarker == null || peakLine == null || 
                peakValueText == null || testPoint1 == null || testPoint2 == null || testPoint3 == null)
                return;
                
            // 创建新的波形点集合
            PointCollection newPoints = new PointCollection();
            
            // 波形振幅变化
            double amplitudeVariation = 5 * Math.Sin(updateCounter) + random.NextDouble() * 3 - 1.5;
            
            // 波形相位变化
            double phaseShift = 10 * Math.Sin(updateCounter * 0.2);
            
            // 更新曲线上的每个点
            for (int i = 0; i < baseWaveformPoints.Count; i++)
            {
                Point basePoint = baseWaveformPoints[i];
                
                // 计算新的Y坐标（振幅变化）
                double newY = basePoint.Y;
                
                // 添加周期性变化
                if (basePoint.Y < 155) // 不要影响基线
                {
                    double normalizedX = basePoint.X / 250.0; // 归一化X坐标
                    double vibration = amplitudeVariation * Math.Sin((normalizedX * 6 + updateCounter) * Math.PI);
                    
                    // 应用相位变化
                    double xOffset = phaseShift * Math.Sin(normalizedX * Math.PI);
                    double normalizedAdjustedX = (basePoint.X + xOffset) / 250.0;
                    if (normalizedAdjustedX < 0) normalizedAdjustedX = 0;
                    if (normalizedAdjustedX > 1) normalizedAdjustedX = 1;
                    
                    // 中间部分（波形最低点）变化更大
                    double middleEnhancer = 1.0;
                    if (basePoint.Y < 100)
                    {
                        // 波峰附近的变化更明显
                        middleEnhancer = 1.5;
                    }
                    
                    newY = basePoint.Y + vibration * middleEnhancer;
                    
                    // 确保Y值在合理范围内
                    if (newY < 2) newY = 2;
                    if (newY > 160) newY = 160;
                }
                
                newPoints.Add(new Point(basePoint.X, newY));
            }
            
            // 更新波形
            waveform.Points = newPoints;
            
            // 查找最低点（对应最高场强）
            double minY = double.MaxValue;
            int minIndex = 0;
            
            for (int i = 0; i < newPoints.Count; i++)
            {
                if (newPoints[i].Y < minY)
                {
                    minY = newPoints[i].Y;
                    minIndex = i;
                }
            }
            
            // 更新最高场强标记位置
            double peakX = newPoints[minIndex].X;
            double peakY = newPoints[minIndex].Y;
            
            // 计算新的峰值场强
            peakValue = 25.8 + (160 - peakY) / 20.0 + random.NextDouble() * 0.6 - 0.3;
            peakValue = Math.Round(peakValue * 10) / 10.0; // 四舍五入到小数点后一位
            
            // 更新标记位置
            Canvas.SetLeft(peakMarker, peakX - 3); // 居中偏移
            Canvas.SetTop(peakMarker, peakY - 3); // 居中偏移
            
            peakLine.X1 = peakX;
            peakLine.X2 = peakX;
            peakLine.Y1 = peakY;
            peakLine.Y2 = peakY - 15;
            
            Canvas.SetLeft(peakValueText, peakX - 20); // 文本居中
            Canvas.SetTop(peakValueText, peakY - 25); // 文本位置
            
            // 更新峰值文本
            peakValueText.Text = $"{peakValue:F1} kV/cm";
            
            // 更新测试点位置
            UpdateTestPoints(newPoints, testPoint1, testPoint2, testPoint3);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine("更新电场波形时发生错误: " + ex.Message);
        }
    }
    
    /// <summary>
    /// 更新测试点位置
    /// </summary>
    private void UpdateTestPoints(PointCollection points, Ellipse testPoint1, Ellipse testPoint2, Ellipse testPoint3)
    {
        // 更新测试点1位置 (位于第一个测量点附近)
        double x1 = 62.5;
        double y1 = GetYValueAtX(points, x1);
        Canvas.SetTop(testPoint1, y1 - 2.5); // 居中偏移
        
        // 更新测试点2位置 (位于第二个测量点附近)
        double x2 = 125;
        double y2 = GetYValueAtX(points, x2);
        Canvas.SetTop(testPoint2, y2 - 2.5); // 居中偏移
        
        // 更新测试点3位置 (位于第三个测量点附近)
        double x3 = 187.5;
        double y3 = GetYValueAtX(points, x3);
        Canvas.SetTop(testPoint3, y3 - 2.5); // 居中偏移
    }
    
    /// <summary>
    /// 获取曲线上指定X坐标处的Y值
    /// </summary>
    private double GetYValueAtX(PointCollection points, double x)
    {
        // 找到最接近的点
        for (int i = 0; i < points.Count - 1; i++)
        {
            if (points[i].X <= x && points[i + 1].X >= x)
            {
                // 线性插值
                double ratio = (x - points[i].X) / (points[i + 1].X - points[i].X);
                return points[i].Y + ratio * (points[i + 1].Y - points[i].Y);
            }
        }
        
        // 如果没有找到合适的点，返回默认值
        return 80;
    }
    
    /// <summary>
    /// 更新测量点数据
    /// </summary>
    private void UpdateMeasurementData()
    {
        try 
        {
            // 查找电场测量面板
            var panel3D = FindVisualChildren<Controls.Panel3D>(this)
                .FirstOrDefault(p => p.Header != null && p.Header.ToString() == "电场测量");
                
            if (panel3D == null)
                return;
                
            // 查找数据文本控件
            var p1FieldStrength = FindVisualChildren<TextBlock>(panel3D).FirstOrDefault(t => t.Tag != null && t.Tag.ToString() == "p1FieldStrength");
            var p1PeakTime = FindVisualChildren<TextBlock>(panel3D).FirstOrDefault(t => t.Tag != null && t.Tag.ToString() == "p1PeakTime");
            var p2FieldStrength = FindVisualChildren<TextBlock>(panel3D).FirstOrDefault(t => t.Tag != null && t.Tag.ToString() == "p2FieldStrength");
            var p2PeakTime = FindVisualChildren<TextBlock>(panel3D).FirstOrDefault(t => t.Tag != null && t.Tag.ToString() == "p2PeakTime");
            var p3FieldStrength = FindVisualChildren<TextBlock>(panel3D).FirstOrDefault(t => t.Tag != null && t.Tag.ToString() == "p3FieldStrength");
            var p3PeakTime = FindVisualChildren<TextBlock>(panel3D).FirstOrDefault(t => t.Tag != null && t.Tag.ToString() == "p3PeakTime");
            
            if (p1FieldStrength == null || p1PeakTime == null || 
                p2FieldStrength == null || p2PeakTime == null || 
                p3FieldStrength == null || p3PeakTime == null)
                return;
            
            // 更新P1点的数据
            double p1Value = 15.3 + 2 * Math.Sin(updateCounter * 0.7) + random.NextDouble() * 0.6 - 0.3;
            p1Value = Math.Round(p1Value * 10) / 10.0; // 四舍五入到小数点后一位
            p1FieldStrength.Text = p1Value.ToString("F1");
            
            int p1Time = (int)(40 + 5 * Math.Sin(updateCounter * 0.5) + random.Next(-2, 3));
            p1PeakTime.Text = p1Time.ToString();
            
            // 更新P2点的数据
            double p2Value = peakValue; // 使用峰值
            p2FieldStrength.Text = p2Value.ToString("F1");
            
            int p2Time = (int)(75 + 8 * Math.Sin(updateCounter * 0.3) + random.Next(-3, 4));
            p2PeakTime.Text = p2Time.ToString();
            
            // 更新P3点的数据
            double p3Value = 18.5 + 3 * Math.Sin(updateCounter * 0.6) + random.NextDouble() * 0.8 - 0.4;
            p3Value = Math.Round(p3Value * 10) / 10.0; // 四舍五入到小数点后一位
            p3FieldStrength.Text = p3Value.ToString("F1");
            
            int p3Time = (int)(90 + 10 * Math.Sin(updateCounter * 0.4) + random.Next(-4, 5));
            p3PeakTime.Text = p3Time.ToString();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine("更新测量数据时发生错误: " + ex.Message);
        }
    }
    
    /// <summary>
    /// 更新控制参数
    /// </summary>
    private void UpdateControlParameters()
    {
        try
        {
            // 查找电场测量面板
            var panel3D = FindVisualChildren<Controls.Panel3D>(this)
                .FirstOrDefault(p => p.Header != null && p.Header.ToString() == "电场测量");
                
            if (panel3D == null)
                return;
                
            // 查找控制参数控件
            var voltageProgressBar = FindVisualChildren<ProgressBar>(panel3D).FirstOrDefault(p => p.Tag != null && p.Tag.ToString() == "voltageProgressBar");
            var voltageMinValue = FindVisualChildren<TextBlock>(panel3D).FirstOrDefault(t => t.Tag != null && t.Tag.ToString() == "voltageMinValue");
            var riseTimeProgressBar = FindVisualChildren<ProgressBar>(panel3D).FirstOrDefault(p => p.Tag != null && p.Tag.ToString() == "riseTimeProgressBar");
            var riseTimeMinValue = FindVisualChildren<TextBlock>(panel3D).FirstOrDefault(t => t.Tag != null && t.Tag.ToString() == "riseTimeMinValue");
            
            if (voltageProgressBar == null || voltageMinValue == null || 
                riseTimeProgressBar == null || riseTimeMinValue == null)
                return;
            
            // 更新峰值电压
            voltageValue = 800 + 20 * Math.Sin(updateCounter * 0.3) + random.Next(-10, 11);
            voltageMinValue.Text = $"{voltageValue:F0}kV";
            
            // 更新峰值电压进度条
            double normalizedVoltage = (voltageValue - 800) / 200.0 * 2.0 + 8.0; // 归一化到进度条范围 (8-10)
            voltageProgressBar.Value = normalizedVoltage;
            
            // 更新上升时间
            riseTimeValue = 50 + 5 * Math.Sin(updateCounter * 0.5) + random.Next(-3, 4);
            riseTimeMinValue.Text = $"{riseTimeValue:F0}ns";
            
            // 更新上升时间进度条
            double normalizedRiseTime = (riseTimeValue - 50) / 50.0 * 5.0 + 5.0; // 归一化到进度条范围 (5-10)
            riseTimeProgressBar.Value = normalizedRiseTime;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine("更新控制参数时发生错误: " + ex.Message);
        }
    }
}