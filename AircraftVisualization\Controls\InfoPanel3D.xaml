<UserControl x:Class="AircraftVisualization.Controls.InfoPanel3D"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AircraftVisualization.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="100" d:DesignWidth="400">
    <UserControl.Resources>
        <LinearGradientBrush x:Key="GreenPanelGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FF003300" Offset="0"/>
            <GradientStop Color="#FF006600" Offset="1"/>
        </LinearGradientBrush>
    </UserControl.Resources>
    
    <Grid Margin="0,0,0,10">
        <!-- 添加阴影效果 -->
        <Border Background="#FF001100" Margin="3,3,0,0" CornerRadius="5"/>
        
        <!-- 主面板 -->
        <Border Background="{StaticResource GreenPanelGradient}" BorderBrush="#FF00AA00" BorderThickness="1" CornerRadius="5">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.3" BlurRadius="10"/>
            </Border.Effect>
            
            <Grid Margin="5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 左侧圆形图标 -->
                <Border Grid.Column="0" Width="24" Height="24" Background="#FF001100" CornerRadius="12" 
                        BorderBrush="#FF00AA00" BorderThickness="1" Margin="0,0,10,0" VerticalAlignment="Top">
                    <Border.Effect>
                        <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.5" BlurRadius="5"/>
                    </Border.Effect>
                    
                    <TextBlock x:Name="IconText" Text="i" Foreground="#FF00FF00" 
                               HorizontalAlignment="Center" VerticalAlignment="Center"
                               FontWeight="Bold" FontSize="14"/>
                </Border>
                
                <!-- 右侧文本内容 -->
                <StackPanel Grid.Column="1">
                    <TextBlock x:Name="TitleText" Text="三维可视化" Foreground="#FFFFFFFF" 
                               FontSize="14" FontWeight="Bold" Margin="0,0,0,5">
                        <TextBlock.Effect>
                            <DropShadowEffect ShadowDepth="1" Color="Black" Opacity="0.7" BlurRadius="2"/>
                        </TextBlock.Effect>
                    </TextBlock>

                    <TextBlock x:Name="ContentText" Text="初始阶段可动态展示装置初始加电过程、气压调节过程,运行阶段可动态展示 Marx 建立(开关逐个击穿)，中间电容充电、中间开关击穿、末端充电、末端开关击穿、天线加电以及空间电场的形成过程。。" 
                               Foreground="#FFE0E0E0" FontSize="12" TextWrapping="Wrap">
                        <TextBlock.Effect>
                            <DropShadowEffect ShadowDepth="1" Color="Black" Opacity="0.5" BlurRadius="1"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
