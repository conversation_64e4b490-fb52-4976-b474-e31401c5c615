﻿<Window x:Class="AircraftVisualization.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AircraftVisualization"
        xmlns:controls="clr-namespace:AircraftVisualization.Controls"
        mc:Ignorable="d"
        Title="装置可视化控制系统" Height="768" Width="1280"
        WindowStartupLocation="CenterScreen"
        Background="#FF0A0A0A"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="CanResizeWithGrip">
    <Window.Resources>
        <!-- 定义渐变色和样式 -->
        <LinearGradientBrush x:Key="GreenGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FF00FF00" Offset="0"/>
            <GradientStop Color="#FF003300" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="OrangeGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FFFF6600" Offset="0"/>
            <GradientStop Color="#FF993300" Offset="1"/>
        </LinearGradientBrush>
        
        <!-- 窗口控制按钮样式 -->
        <Style x:Key="WindowControlButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="35"/>
            <Setter Property="Height" Value="25"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="#FFEEEEEE"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="2,0,0,0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#3300FF00"/>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.5" BlurRadius="5"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#6600FF00"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 关闭按钮特殊样式 -->
        <Style x:Key="CloseButtonStyle" TargetType="Button" BasedOn="{StaticResource WindowControlButtonStyle}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#33FF0000"/>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect ShadowDepth="0" Color="#FFFF0000" Opacity="0.5" BlurRadius="5"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#66FF0000"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PanelHeaderStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#FFEEEEEE"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>

        <Style x:Key="DataTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#FF00FF00"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="2"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="280"/>
        </Grid.RowDefinitions>
        
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 标题栏区域 -->
        <Grid Grid.Row="0" Grid.ColumnSpan="3" Background="Transparent" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
            <!-- TechHeader 控件 -->
            <controls:TechHeader HeaderTitle="装置可视化控制系统"/>
            
            <!-- 窗口控制按钮 -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,5,5,0">
                <Button x:Name="btnMinimize" Style="{StaticResource WindowControlButtonStyle}" Click="BtnMinimize_Click" ToolTip="最小化">
                    <TextBlock Text="&#xE921;" FontFamily="Segoe MDL2 Assets" Foreground="#FF00FF00">
                        <TextBlock.Effect>
                            <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.5" BlurRadius="3"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </Button>
                <Button x:Name="btnMaximize" Style="{StaticResource WindowControlButtonStyle}" Click="BtnMaximize_Click" ToolTip="最大化">
                    <TextBlock x:Name="MaximizeIcon" Text="&#xE922;" FontFamily="Segoe MDL2 Assets" Foreground="#FF00FF00">
                        <TextBlock.Effect>
                            <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.5" BlurRadius="3"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </Button>
                <Button x:Name="btnClose" Style="{StaticResource CloseButtonStyle}" Click="BtnClose_Click" ToolTip="关闭">
                    <TextBlock Text="&#xE8BB;" FontFamily="Segoe MDL2 Assets" Foreground="#FFFF3333">
                        <TextBlock.Effect>
                            <DropShadowEffect ShadowDepth="0" Color="#FFFF0000" Opacity="0.5" BlurRadius="3"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </Button>
            </StackPanel>
        </Grid>

        <!-- 中央视频显示区域 - 扩展到整个界面的上半部分 -->
        <Grid Grid.Row="1" Grid.ColumnSpan="3" Margin="10,10,10,10">
            <!-- 雷达背景 -->
            <Ellipse Width="600" Height="600" StrokeThickness="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Ellipse.Stroke>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#FF003300" Offset="0"/>
                        <GradientStop Color="#FF00FF00" Offset="1"/>
                    </LinearGradientBrush>
                </Ellipse.Stroke>
            </Ellipse>

            <Ellipse Width="480" Height="480" StrokeThickness="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Ellipse.Stroke>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#FF003300" Offset="0"/>
                        <GradientStop Color="#FF00FF00" Offset="1"/>
                    </LinearGradientBrush>
                </Ellipse.Stroke>
            </Ellipse>

            <Ellipse Width="360" Height="360" StrokeThickness="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Ellipse.Stroke>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#FF003300" Offset="0"/>
                        <GradientStop Color="#FF00FF00" Offset="1"/>
                    </LinearGradientBrush>
                </Ellipse.Stroke>
            </Ellipse>

            <Ellipse Width="240" Height="240" StrokeThickness="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Ellipse.Stroke>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#FF003300" Offset="0"/>
                        <GradientStop Color="#FF00FF00" Offset="1"/>
                    </LinearGradientBrush>
                </Ellipse.Stroke>
            </Ellipse>

            <Ellipse Width="120" Height="120" StrokeThickness="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Ellipse.Stroke>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#FF003300" Offset="0"/>
                        <GradientStop Color="#FF00FF00" Offset="1"/>
                    </LinearGradientBrush>
                </Ellipse.Stroke>
            </Ellipse>

            <!-- 十字线 -->
            <Line X1="340" Y1="0" X2="340" Y2="680" Stroke="#FF003300" StrokeThickness="1" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            <Line Y1="340" X1="0" Y2="340" X2="680" Stroke="#FF003300" StrokeThickness="1" HorizontalAlignment="Center" VerticalAlignment="Center"/>

            <!-- 视频播放器和信息框区域的布局 -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="240"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="240"/>
                </Grid.ColumnDefinitions>
                
                <!-- 左侧信息框区域 -->
                <StackPanel Grid.Column="0" VerticalAlignment="Center" Margin="0,0,10,0">
                    <!-- 涂料与航电信息框 - 左侧 -->
                    <controls:InfoPanel3D
                        HorizontalAlignment="Stretch" VerticalAlignment="Center"
                        Margin="0,0,0,10"
                        Icon="⚠"
                        Title="控制系统"
                        Content="气路控制和电路控制，控制装置的运行过程，需要读取测量电脑的波形结果作为反馈控制，通过强化学习模型自动选代生成下发次的气压/电路参数。"
                        Opacity="0.9"/>
                    
                    <!-- 技术说明信息框 - 左侧下方 -->
                    <controls:InfoPanel3D
                        HorizontalAlignment="Stretch" VerticalAlignment="Center"
                        Margin="0,10,0,0"
                        Icon="i"
                        Title="测量系统"
                        Content="电场测量、波形处理(自动)及数据库存储功能。需要读取控制电脑的参数设置，与波形统一保存，实现单次试验数据的统一保存。"
                        Opacity="0.9"/>
                </StackPanel>
                
                <!-- 中央视频播放区域 -->
                <Grid Grid.Column="1" HorizontalAlignment="Center">
                    <!-- 视频播放器 - 循环播放充电流程 - 进一步放大尺寸 -->
                    <MediaElement x:Name="videoPlayer" 
                              Width="720" Height="405"
                              Source="Resources\充电流程.mp4"
                              LoadedBehavior="Manual"
                              UnloadedBehavior="Stop"
                              Stretch="Uniform"
                              MediaEnded="VideoPlayer_MediaEnded"
                              HorizontalAlignment="Center" 
                              VerticalAlignment="Center">
                        <MediaElement.Effect>
                            <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.3" BlurRadius="15"/>
                        </MediaElement.Effect>
                        <MediaElement.OpacityMask>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#FF000000" Offset="0.0"/>
                                <GradientStop Color="#EE000000" Offset="0.1"/>
                                <GradientStop Color="#DD000000" Offset="0.9"/>
                                <GradientStop Color="#AA000000" Offset="1.0"/>
                            </LinearGradientBrush>
                        </MediaElement.OpacityMask>
                    </MediaElement>

                    <!-- 视频播放器边框 -->
                    <Border Width="736" Height="421" BorderThickness="2" CornerRadius="5" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Border.BorderBrush>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#FF00FF00" Offset="0"/>
                                <GradientStop Color="#FF003300" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.BorderBrush>
                        <Border.Effect>
                            <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.5" BlurRadius="10"/>
                        </Border.Effect>
                    </Border>

                    <!-- 视频标题 -->
                    <TextBlock Text="充电流程演示" Foreground="#FF00FF00" FontSize="22"
                           HorizontalAlignment="Center" VerticalAlignment="Center"
                           Margin="0,-220,0,0">
                        <TextBlock.Effect>
                            <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.7" BlurRadius="5"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </Grid>
                
                <!-- 右侧信息框区域 -->
                <StackPanel Grid.Column="2" VerticalAlignment="Center" Margin="10,0,0,0">
                    <!-- 基本信息信息框 - 右侧 -->
                    <controls:InfoPanel3D
                        HorizontalAlignment="Stretch" VerticalAlignment="Center"
                        Margin="0,0,0,0"
                        Icon="ℹ"
                        Title="三维可视化"
                        Content="初始动作自动动态展示装置内加装电池，气压调节过程,后序自动展示 Mars 建立(开关各个步骤)，中间结合充电, 中间开关位置，末端充电，末端开关，关闭阀门以及空间电极结构的准备过程。"
                        Opacity="0.9"/>
                </StackPanel>
            </Grid>
        </Grid>

        <!-- 底部信息框区域 -->
        <Grid Grid.Row="2" Grid.ColumnSpan="3" Margin="10,0,10,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧面板区域 -->
            <Grid Grid.Column="0" Margin="0,0,5,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- 区域武装设备面板 -->
                <controls:Panel3D Grid.Row="0" Header="气路控制" SubHeader="气路控制和电路控制，控制装置的运行过程" Margin="0,0,0,5">
                    <controls:Panel3D.PanelContent>
                        <UniformGrid Rows="1" Columns="4">
                            <controls:DataBox3D Title="数量" Value="0" Unit="/V"/>
                            <controls:DataBox3D Title="数量" Value="0" Unit="/V"/>
                            <controls:DataBox3D Title="数量" Value="0" Unit="/V"/>
                            <controls:DataBox3D Title="数量" Value="181" Unit="/V"/>
                        </UniformGrid>
                    </controls:Panel3D.PanelContent>
                </controls:Panel3D>
                
                <!-- 战区机型分布面板 -->
                <controls:Panel3D Grid.Row="1" Header="电路控制" SubHeader="气路控制和电路控制，控制装置的运行过程" Margin="0,5,0,0">
                    <controls:Panel3D.PanelContent>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- 左侧圆形进度条 -->
                            <Grid Grid.Column="0" Margin="10">
                                <!--<Ellipse Width="80" Height="80" Stroke="#FF333333" StrokeThickness="8"/>
                                <Path Stroke="#FF00FF00" StrokeThickness="8" Height="41" VerticalAlignment="Top">
                                    <Path.Data>
                                        <PathGeometry>
                                            <PathFigure StartPoint="40,0">
                                                <ArcSegment Size="40,40" IsLargeArc="False"
                                                            SweepDirection="Clockwise"
                                                            Point="40,5"/>
                                            </PathFigure>
                                        </PathGeometry>
                                    </Path.Data>
                                </Path>-->
                                <TextBlock Text="7%" Foreground="#FF00FF00"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontSize="20">
                                    <TextBlock.Effect>
                                        <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.5" BlurRadius="5"/>
                                    </TextBlock.Effect>
                                </TextBlock>
                                <TextBlock Text="机型" Foreground="#FFAAAAAA"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Bottom"
                                           FontSize="10" Margin="0,0,0,10"/>
                                <TextBlock Text="#11" Foreground="#FFAAAAAA"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Bottom"
                                           FontSize="10"/>
                            </Grid>

                            <!-- 右侧圆形进度条 -->
                            <Grid Grid.Column="1" Margin="10">
                                <!--<Ellipse Width="80" Height="80" Stroke="#FF333333" StrokeThickness="8"/>
                                <Path Stroke="#FF00FF00" StrokeThickness="8">
                                    <Path.Data>
                                        <PathGeometry>
                                            <PathFigure StartPoint="40,0">
                                                <ArcSegment Size="40,40" IsLargeArc="False"
                                                            SweepDirection="Clockwise"
                                                            Point="60,60"/>
                                            </PathFigure>
                                        </PathGeometry>
                                    </Path.Data>
                                </Path>-->
                                <TextBlock Text="22%" Foreground="#FF00FF00"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontSize="20">
                                    <TextBlock.Effect>
                                        <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.5" BlurRadius="5"/>
                                    </TextBlock.Effect>
                                </TextBlock>
                                <TextBlock Text="机型" Foreground="#FFAAAAAA"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Bottom"
                                           FontSize="10" Margin="0,0,0,10"/>
                                <TextBlock Text="#10" Foreground="#FFAAAAAA"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Bottom"
                                           FontSize="10"/>
                            </Grid>
                        </Grid>
                    </controls:Panel3D.PanelContent>
                </controls:Panel3D>
            </Grid>

            <!-- 中间信息框区域 -->
            <StackPanel Grid.Column="1" Margin="5,0,5,0">
                <!-- 关键波形显示面板 -->
                <controls:Panel3D x:Name="waveformPanel" Header="关键波形显示" SubHeader="动态展示过程中,展示关键波形,展示明确的波形关键性参数">
                    <controls:Panel3D.PanelContent>
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Canvas Grid.Row="0" Height="150">
                                <!-- 背景网格线 -->
                                <Line X1="0" Y1="0" X2="250" Y2="0" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="0" Y1="37.5" X2="250" Y2="37.5" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="0" Y1="75" X2="250" Y2="75" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="0" Y1="112.5" X2="250" Y2="112.5" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="0" Y1="150" X2="250" Y2="150" Stroke="#FF333333" StrokeThickness="1"/>
                                
                                <Line X1="0" Y1="0" X2="0" Y2="150" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="62.5" Y1="0" X2="62.5" Y2="150" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="125" Y1="0" X2="125" Y2="150" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="187.5" Y1="0" X2="187.5" Y2="150" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="250" Y1="0" X2="250" Y2="150" Stroke="#FF333333" StrokeThickness="1"/>

                                <!-- Marx建立波形 (默认显示) -->
                                <Polyline x:Uid="marx_waveform" Tag="marxWaveform" Visibility="Visible"
                                         Points="0,150 10,145 20,130 30,100 40,70 50,45 60,30 70,20 80,15 90,12 100,10 110,10 120,10 130,12 140,15 150,17 160,18 170,19 180,19 190,20 200,20 210,20 220,20 230,20 240,20 250,20" 
                                         Stroke="#FF00FF00" StrokeThickness="2">
                                    <Polyline.Effect>
                                        <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.3" BlurRadius="5"/>
                                    </Polyline.Effect>
                                </Polyline>
                                
                                <!-- 中间波形 (初始隐藏) -->
                                <Polyline x:Uid="intermediate_waveform" Tag="intermediateWaveform" Visibility="Collapsed"
                                         Points="0,150 10,140 20,120 30,90 40,60 50,35 60,20 70,10 80,5 90,3 100,3 110,3 120,5 130,7 140,9 150,10 160,10 170,9 180,8 190,7 200,8 210,10 220,13 230,15 240,15 250,15" 
                                         Stroke="#FFFF9900" StrokeThickness="2">
                                    <Polyline.Effect>
                                        <DropShadowEffect ShadowDepth="0" Color="#FFFF9900" Opacity="0.3" BlurRadius="5"/>
                                    </Polyline.Effect>
                                </Polyline>
                                
                                <!-- 最终波形 (初始隐藏) -->
                                <Polyline x:Uid="final_waveform" Tag="finalWaveform" Visibility="Collapsed"
                                         Points="0,150 10,130 20,100 30,70 40,40 50,20 60,10 70,5 80,3 90,2 100,2 110,2 120,2 130,2 140,2 150,2 160,3 170,4 180,6 190,9 200,12 210,15 220,18 230,20 240,25 250,30" 
                                         Stroke="#FFFF3333" StrokeThickness="2">
                                    <Polyline.Effect>
                                        <DropShadowEffect ShadowDepth="0" Color="#FFFF3333" Opacity="0.3" BlurRadius="5"/>
                                    </Polyline.Effect>
                                </Polyline>
                                
                                <!-- Marx建立波形的最高电压标记 (默认显示) -->
                                <Ellipse x:Uid="marx_marker_point" Tag="marxMarkerPoint" Canvas.Left="240" Canvas.Top="18" Width="6" Height="6" Fill="#FFFF6600" Visibility="Visible"/>
                                <Line x:Uid="marx_marker_line" Tag="marxMarkerLine" X1="240" Y1="20" X2="240" Y2="5" Stroke="#FFFF6600" StrokeThickness="1" StrokeDashArray="2,2" Visibility="Visible"/>
                                <TextBlock x:Uid="marx_marker_value" Tag="marxMarkerValue" Canvas.Left="225" Canvas.Top="0" Text="800kV" Foreground="#FFFF6600" FontSize="10" Visibility="Visible"/>
                                <TextBlock x:Uid="marx_label" Tag="marxLabel" Canvas.Left="5" Canvas.Top="5" Text="Marx建立" Foreground="#FF00FF00" FontSize="10" Visibility="Visible"/>
                                
                                <!-- 中间波形的最高电压标记 (初始隐藏) -->
                                <Ellipse x:Uid="intermediate_marker_point" Tag="intermediateMarkerPoint" Canvas.Left="70" Canvas.Top="8" Width="6" Height="6" Fill="#FFFF6600" Visibility="Collapsed"/>
                                <Line x:Uid="intermediate_marker_line" Tag="intermediateMarkerLine" X1="70" Y1="10" X2="70" Y2="0" Stroke="#FFFF6600" StrokeThickness="1" StrokeDashArray="2,2" Visibility="Collapsed"/>
                                <TextBlock x:Uid="intermediate_marker_value" Tag="intermediateMarkerValue" Canvas.Left="55" Canvas.Top="0" Text="920kV" Foreground="#FFFF6600" FontSize="10" Visibility="Collapsed"/>
                                <TextBlock x:Uid="intermediate_label" Tag="intermediateLabel" Canvas.Left="5" Canvas.Top="5" Text="中间波形" Foreground="#FFFF9900" FontSize="10" Visibility="Collapsed"/>
                                
                                <!-- 最终波形的最高电压标记 (初始隐藏) -->
                                <Ellipse x:Uid="final_marker_point" Tag="finalMarkerPoint" Canvas.Left="150" Canvas.Top="0" Width="6" Height="6" Fill="#FFFF6600" Visibility="Collapsed"/>
                                <Line x:Uid="final_marker_line" Tag="finalMarkerLine" X1="150" Y1="2" X2="150" Y2="0" Stroke="#FFFF6600" StrokeThickness="1" StrokeDashArray="2,2" Visibility="Collapsed"/>
                                <TextBlock x:Uid="final_marker_value" Tag="finalMarkerValue" Canvas.Left="130" Canvas.Top="0" Text="1.05MV" Foreground="#FFFF6600" FontSize="10" Visibility="Collapsed"/>
                                <TextBlock x:Uid="final_label" Tag="finalLabel" Canvas.Left="5" Canvas.Top="5" Text="最终波形" Foreground="#FFFF3333" FontSize="10" Visibility="Collapsed"/>
                            </Canvas>

                            <!-- 波形类型选择 -->
                            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5,0,0">
                                <RadioButton x:Uid="rb_marx" Tag="marx" Content="Marx建立" Foreground="#FFAAAAAA" Margin="5,0" IsChecked="True" Checked="WaveformRadioButton_Checked"/>
                                <RadioButton x:Uid="rb_intermediate" Tag="intermediate" Content="中间波形" Foreground="#FFAAAAAA" Margin="5,0" Checked="WaveformRadioButton_Checked"/>
                                <RadioButton x:Uid="rb_final" Tag="final" Content="最终波形" Foreground="#FFAAAAAA" Margin="5,0" Checked="WaveformRadioButton_Checked"/>
                            </StackPanel>
                            
                            <!-- 关键参数显示区域 -->
                            <Grid Grid.Row="2" Margin="0,5,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- Marx建立波形参数 (默认显示) -->
                                <Grid x:Uid="marx_parameters" Tag="marxParameters" Visibility="Visible">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <Border Grid.Column="0" Background="#22FFFFFF" CornerRadius="3" Margin="2">
                                        <StackPanel Margin="5,2">
                                            <TextBlock Text="最高电压" Foreground="#FFAAAAAA" FontSize="9" HorizontalAlignment="Center"/>
                                            <TextBlock Text="800 kV" Foreground="#FF00FF00" FontSize="12" HorizontalAlignment="Center">
                                                <TextBlock.Effect>
                                                    <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.5" BlurRadius="3"/>
                                                </TextBlock.Effect>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                    
                                    <Border Grid.Column="1" Background="#22FFFFFF" CornerRadius="3" Margin="2">
                                        <StackPanel Margin="5,2">
                                            <TextBlock Text="上升时间" Foreground="#FFAAAAAA" FontSize="9" HorizontalAlignment="Center"/>
                                            <TextBlock Text="50 ns" Foreground="#FF00FF00" FontSize="12" HorizontalAlignment="Center">
                                                <TextBlock.Effect>
                                                    <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.5" BlurRadius="3"/>
                                                </TextBlock.Effect>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                    
                                    <Border Grid.Column="2" Background="#22FFFFFF" CornerRadius="3" Margin="2">
                                        <StackPanel Margin="5,2">
                                            <TextBlock Text="保持时间" Foreground="#FFAAAAAA" FontSize="9" HorizontalAlignment="Center"/>
                                            <TextBlock Text="125 ns" Foreground="#FF00FF00" FontSize="12" HorizontalAlignment="Center">
                                                <TextBlock.Effect>
                                                    <DropShadowEffect ShadowDepth="0" Color="#FF00FF00" Opacity="0.5" BlurRadius="3"/>
                                                </TextBlock.Effect>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                </Grid>
                                
                                <!-- 中间波形参数 (初始隐藏) -->
                                <Grid x:Uid="intermediate_parameters" Tag="intermediateParameters" Visibility="Collapsed">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <Border Grid.Column="0" Background="#22FFFFFF" CornerRadius="3" Margin="2">
                                        <StackPanel Margin="5,2">
                                            <TextBlock Text="最高电压" Foreground="#FFAAAAAA" FontSize="9" HorizontalAlignment="Center"/>
                                            <TextBlock Text="920 kV" Foreground="#FFFF9900" FontSize="12" HorizontalAlignment="Center">
                                                <TextBlock.Effect>
                                                    <DropShadowEffect ShadowDepth="0" Color="#FFFF9900" Opacity="0.5" BlurRadius="3"/>
                                                </TextBlock.Effect>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                    
                                    <Border Grid.Column="1" Background="#22FFFFFF" CornerRadius="3" Margin="2">
                                        <StackPanel Margin="5,2">
                                            <TextBlock Text="上升时间" Foreground="#FFAAAAAA" FontSize="9" HorizontalAlignment="Center"/>
                                            <TextBlock Text="75 ns" Foreground="#FFFF9900" FontSize="12" HorizontalAlignment="Center">
                                                <TextBlock.Effect>
                                                    <DropShadowEffect ShadowDepth="0" Color="#FFFF9900" Opacity="0.5" BlurRadius="3"/>
                                                </TextBlock.Effect>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                    
                                    <Border Grid.Column="2" Background="#22FFFFFF" CornerRadius="3" Margin="2">
                                        <StackPanel Margin="5,2">
                                            <TextBlock Text="保持时间" Foreground="#FFAAAAAA" FontSize="9" HorizontalAlignment="Center"/>
                                            <TextBlock Text="180 ns" Foreground="#FFFF9900" FontSize="12" HorizontalAlignment="Center">
                                                <TextBlock.Effect>
                                                    <DropShadowEffect ShadowDepth="0" Color="#FFFF9900" Opacity="0.5" BlurRadius="3"/>
                                                </TextBlock.Effect>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                </Grid>
                                
                                <!-- 最终波形参数 (初始隐藏) -->
                                <Grid x:Uid="final_parameters" Tag="finalParameters" Visibility="Collapsed">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <Border Grid.Column="0" Background="#22FFFFFF" CornerRadius="3" Margin="2">
                                        <StackPanel Margin="5,2">
                                            <TextBlock Text="最高电压" Foreground="#FFAAAAAA" FontSize="9" HorizontalAlignment="Center"/>
                                            <TextBlock Text="1.05 MV" Foreground="#FFFF3333" FontSize="12" HorizontalAlignment="Center">
                                                <TextBlock.Effect>
                                                    <DropShadowEffect ShadowDepth="0" Color="#FFFF3333" Opacity="0.5" BlurRadius="3"/>
                                                </TextBlock.Effect>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                    
                                    <Border Grid.Column="1" Background="#22FFFFFF" CornerRadius="3" Margin="2">
                                        <StackPanel Margin="5,2">
                                            <TextBlock Text="上升时间" Foreground="#FFAAAAAA" FontSize="9" HorizontalAlignment="Center"/>
                                            <TextBlock Text="100 ns" Foreground="#FFFF3333" FontSize="12" HorizontalAlignment="Center">
                                                <TextBlock.Effect>
                                                    <DropShadowEffect ShadowDepth="0" Color="#FFFF3333" Opacity="0.5" BlurRadius="3"/>
                                                </TextBlock.Effect>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                    
                                    <Border Grid.Column="2" Background="#22FFFFFF" CornerRadius="3" Margin="2">
                                        <StackPanel Margin="5,2">
                                            <TextBlock Text="保持时间" Foreground="#FFAAAAAA" FontSize="9" HorizontalAlignment="Center"/>
                                            <TextBlock Text="250 ns" Foreground="#FFFF3333" FontSize="12" HorizontalAlignment="Center">
                                                <TextBlock.Effect>
                                                    <DropShadowEffect ShadowDepth="0" Color="#FFFF3333" Opacity="0.5" BlurRadius="3"/>
                                                </TextBlock.Effect>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                </Grid>
                            </Grid>
                        </Grid>
                    </controls:Panel3D.PanelContent>
                </controls:Panel3D>
            </StackPanel>

            <!-- 右侧面板区域 -->
            <Grid Grid.Column="2" Margin="5,0,0,0">
                <!-- 电场测量面板 -->
                <controls:Panel3D Header="电场测量" SubHeader="电场测量、波形处理(自动)及数据库存储功能">
                    <controls:Panel3D.PanelContent>
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <!-- 电场波形显示区域 -->
                            <Canvas Grid.Row="0" Height="160" Tag="efMeasurementCanvas">
                                <!-- 背景网格 -->
                                <Rectangle Width="250" Height="160" Fill="#11555555"/>
                                
                                <!-- 网格线 -->
                                <Line X1="0" Y1="0" X2="250" Y2="0" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="0" Y1="40" X2="250" Y2="40" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="0" Y1="80" X2="250" Y2="80" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="0" Y1="120" X2="250" Y2="120" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="0" Y1="160" X2="250" Y2="160" Stroke="#FF333333" StrokeThickness="1"/>
                                
                                <Line X1="0" Y1="0" X2="0" Y2="160" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="62.5" Y1="0" X2="62.5" Y2="160" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="125" Y1="0" X2="125" Y2="160" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="187.5" Y1="0" X2="187.5" Y2="160" Stroke="#FF333333" StrokeThickness="1"/>
                                <Line X1="250" Y1="0" X2="250" Y2="160" Stroke="#FF333333" StrokeThickness="1"/>
                                
                                <!-- 电场波形 -->
                                <Polyline Tag="electricFieldWaveform" 
                                         Points="0,160 20,158 40,150 60,120 80,85 100,60 120,45 140,40 160,43 180,50 200,70 220,100 240,145 250,155" 
                                         Stroke="#FF33CCFF" StrokeThickness="2">
                                    <Polyline.Effect>
                                        <DropShadowEffect ShadowDepth="0" Color="#FF33CCFF" Opacity="0.5" BlurRadius="5"/>
                                    </Polyline.Effect>
                                </Polyline>
                                
                                <!-- 最高场强标记 -->
                                <Ellipse Tag="peakPointMarker" Canvas.Left="140" Canvas.Top="38" Width="6" Height="6" Fill="#FFFF6600"/>
                                <Line Tag="peakPointLine" X1="140" Y1="40" X2="140" Y2="25" Stroke="#FFFF6600" StrokeThickness="1" StrokeDashArray="2,2"/>
                                <TextBlock Tag="peakPointValue" Canvas.Left="120" Canvas.Top="10" Text="25.8 kV/cm" Foreground="#FFFF6600" FontSize="10"/>
                                
                                <!-- 测试点标记 -->
                                <Ellipse Tag="testPoint1" Canvas.Left="62.5" Canvas.Top="118" Width="5" Height="5" Fill="#FFFFFF00"/>
                                <Ellipse Tag="testPoint2" Canvas.Left="125" Canvas.Top="43" Width="5" Height="5" Fill="#FFFFFF00"/>
                                <Ellipse Tag="testPoint3" Canvas.Left="187.5" Canvas.Top="70" Width="5" Height="5" Fill="#FFFFFF00"/>
                                
                                <TextBlock Tag="testPoint1Label" Canvas.Left="55" Canvas.Top="128" Text="P1" Foreground="#FFFFFF00" FontSize="10"/>
                                <TextBlock Tag="testPoint2Label" Canvas.Left="118" Canvas.Top="53" Text="P2" Foreground="#FFFFFF00" FontSize="10"/>
                                <TextBlock Tag="testPoint3Label" Canvas.Left="180" Canvas.Top="80" Text="P3" Foreground="#FFFFFF00" FontSize="10"/>
                            </Canvas>
                            
                            <!-- 测量点数据表格 -->
                            <Grid Grid.Row="1" Margin="0,5,0,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="60"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- 表头 -->
                                <Border Grid.Row="0" Grid.Column="0" Background="#33FFFFFF" Margin="1">
                                    <TextBlock Text="测量点" Foreground="#FFAAAAAA" FontSize="9" Margin="2" HorizontalAlignment="Center"/>
                                </Border>
                                <Border Grid.Row="0" Grid.Column="1" Background="#33FFFFFF" Margin="1">
                                    <TextBlock Text="场强(kV/cm)" Foreground="#FFAAAAAA" FontSize="9" Margin="2" HorizontalAlignment="Center"/>
                                </Border>
                                <Border Grid.Row="0" Grid.Column="2" Background="#33FFFFFF" Margin="1">
                                    <TextBlock Text="峰值时间(ns)" Foreground="#FFAAAAAA" FontSize="9" Margin="2" HorizontalAlignment="Center"/>
                                </Border>
                                
                                <!-- 第一行数据 -->
                                <Border Grid.Row="1" Grid.Column="0" Background="#22FFFFFF" Margin="1">
                                    <TextBlock Text="P1" Foreground="#FFFFFF00" FontSize="10" Margin="2" HorizontalAlignment="Center"/>
                                </Border>
                                <Border Grid.Row="1" Grid.Column="1" Background="#22FFFFFF" Margin="1">
                                    <TextBlock Tag="p1FieldStrength" Text="15.3" Foreground="#FF33CCFF" FontSize="10" Margin="2" HorizontalAlignment="Center"/>
                                </Border>
                                <Border Grid.Row="1" Grid.Column="2" Background="#22FFFFFF" Margin="1">
                                    <TextBlock Tag="p1PeakTime" Text="40" Foreground="#FF33CCFF" FontSize="10" Margin="2" HorizontalAlignment="Center"/>
                                </Border>
                                
                                <!-- 第二行数据 -->
                                <Border Grid.Row="2" Grid.Column="0" Background="#22FFFFFF" Margin="1">
                                    <TextBlock Text="P2" Foreground="#FFFFFF00" FontSize="10" Margin="2" HorizontalAlignment="Center"/>
                                </Border>
                                <Border Grid.Row="2" Grid.Column="1" Background="#22FFFFFF" Margin="1">
                                    <TextBlock Tag="p2FieldStrength" Text="25.8" Foreground="#FF33CCFF" FontSize="10" Margin="2" HorizontalAlignment="Center"/>
                                </Border>
                                <Border Grid.Row="2" Grid.Column="2" Background="#22FFFFFF" Margin="1">
                                    <TextBlock Tag="p2PeakTime" Text="75" Foreground="#FF33CCFF" FontSize="10" Margin="2" HorizontalAlignment="Center"/>
                                </Border>
                                
                                <!-- 第三行数据 -->
                                <Border Grid.Row="3" Grid.Column="0" Background="#22FFFFFF" Margin="1">
                                    <TextBlock Text="P3" Foreground="#FFFFFF00" FontSize="10" Margin="2" HorizontalAlignment="Center"/>
                                </Border>
                                <Border Grid.Row="3" Grid.Column="1" Background="#22FFFFFF" Margin="1">
                                    <TextBlock Tag="p3FieldStrength" Text="18.5" Foreground="#FF33CCFF" FontSize="10" Margin="2" HorizontalAlignment="Center"/>
                                </Border>
                                <Border Grid.Row="3" Grid.Column="2" Background="#22FFFFFF" Margin="1">
                                    <TextBlock Tag="p3PeakTime" Text="90" Foreground="#FF33CCFF" FontSize="10" Margin="2" HorizontalAlignment="Center"/>
                                </Border>
                            </Grid>
                            
                            <!-- 波形控制区域 (整合了之前单独的面板内容) -->
                            <StackPanel Grid.Row="2" Margin="0,5,0,0">
                                <!-- 峰值电压调整 -->
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Width="16" Height="16" Margin="0,0,5,0"
                                            Background="#FF00AA00" CornerRadius="8">
                                        <Border.Effect>
                                            <DropShadowEffect ShadowDepth="0" Color="#FF00AA00" Opacity="0.3" BlurRadius="5"/>
                                        </Border.Effect>
                                    </Border>

                                    <Grid Grid.Column="1">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0" Text="峰值电压调整" Foreground="#FFAAAAAA"
                                                   FontSize="10"/>

                                        <Grid Grid.Row="1">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <ProgressBar Tag="voltageProgressBar" Grid.Column="0" Value="8" Maximum="10" Height="8"
                                                         Foreground="#FF00FF00" Background="#FF333333"/>
                                            <TextBlock Tag="voltageMinValue" Grid.Column="1" Text="800kV" Foreground="#FF00FF00"
                                                       FontSize="10" Margin="5,0"/>
                                            <ProgressBar Grid.Column="2" Value="10" Maximum="10" Height="8"
                                                         Foreground="#FF00FF00" Background="#FF333333"/>
                                            <TextBlock Grid.Column="3" Text="1MV" Foreground="#FF00FF00"
                                                       FontSize="10" Margin="5,0"/>
                                        </Grid>
                                    </Grid>
                                </Grid>

                                <!-- 上升时间调整 -->
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Width="16" Height="16" Margin="0,0,5,0"
                                            Background="#FFFF6600" CornerRadius="8">
                                        <Border.Effect>
                                            <DropShadowEffect ShadowDepth="0" Color="#FFFF6600" Opacity="0.3" BlurRadius="5"/>
                                        </Border.Effect>
                                    </Border>

                                    <Grid Grid.Column="1">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0" Text="上升时间调整" Foreground="#FFAAAAAA"
                                                   FontSize="10"/>

                                        <Grid Grid.Row="1">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <ProgressBar Tag="riseTimeProgressBar" Grid.Column="0" Value="5" Maximum="10" Height="8"
                                                         Foreground="#FF00FF00" Background="#FF333333"/>
                                            <TextBlock Tag="riseTimeMinValue" Grid.Column="1" Text="50ns" Foreground="#FF00FF00"
                                                       FontSize="10" Margin="5,0"/>
                                            <ProgressBar Grid.Column="2" Value="10" Maximum="10" Height="8"
                                                         Foreground="#FF00FF00" Background="#FF333333"/>
                                            <TextBlock Grid.Column="3" Text="100ns" Foreground="#FF00FF00"
                                                       FontSize="10" Margin="5,0"/>
                                        </Grid>
                                    </Grid>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </controls:Panel3D.PanelContent>
                </controls:Panel3D>
            </Grid>
        </Grid>
    </Grid>
</Window>
