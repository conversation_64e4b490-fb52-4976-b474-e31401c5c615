using System.Windows;
using System.Windows.Controls;

namespace AircraftVisualization.Controls
{
    /// <summary>
    /// InfoPanel3D.xaml 的交互逻辑
    /// </summary>
    public partial class InfoPanel3D : UserControl
    {
        public InfoPanel3D()
        {
            InitializeComponent();
        }

        // 图标文本属性
        public string Icon
        {
            get { return (string)GetValue(IconProperty); }
            set { SetValue(IconProperty, value); }
        }

        public static readonly DependencyProperty IconProperty =
            DependencyProperty.Register("Icon", typeof(string), typeof(InfoPanel3D), 
                new PropertyMetadata("i", OnIconChanged));

        private static void OnIconChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            InfoPanel3D panel = d as InfoPanel3D;
            if (panel != null)
            {
                panel.IconText.Text = e.NewValue as string;
            }
        }

        // 标题属性
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register("Title", typeof(string), typeof(InfoPanel3D), 
                new PropertyMetadata("涂料与航电", OnTitleChanged));

        private static void OnTitleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            InfoPanel3D panel = d as InfoPanel3D;
            if (panel != null)
            {
                panel.TitleText.Text = e.NewValue as string;
            }
        }

        // 内容属性
        public string Content
        {
            get { return (string)GetValue(ContentProperty); }
            set { SetValue(ContentProperty, value); }
        }

        public static readonly DependencyProperty ContentProperty =
            DependencyProperty.Register("Content", typeof(string), typeof(InfoPanel3D), 
                new PropertyMetadata("11月中旬央视专题节目称百航发动机为过渡产品，模块化设计是未来研制完成挑战，最终与F-22同等的推重比是该领域能力是未来的目标。", OnContentChanged));

        private static void OnContentChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            InfoPanel3D panel = d as InfoPanel3D;
            if (panel != null)
            {
                panel.ContentText.Text = e.NewValue as string;
            }
        }
    }
}
