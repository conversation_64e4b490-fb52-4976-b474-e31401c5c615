using System.Windows;
using System.Windows.Controls;

namespace AircraftVisualization.Controls
{
    /// <summary>
    /// DataBox3D.xaml 的交互逻辑
    /// </summary>
    public partial class DataBox3D : UserControl
    {
        public DataBox3D()
        {
            InitializeComponent();
        }

        // 标题属性
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register("Title", typeof(string), typeof(DataBox3D), 
                new PropertyMetadata("数据标题", OnTitleChanged));

        private static void OnTitleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            DataBox3D box = d as DataBox3D;
            if (box != null)
            {
                box.TitleText.Text = e.NewValue as string;
            }
        }

        // 值属性
        public string Value
        {
            get { return (string)GetValue(ValueProperty); }
            set { SetValue(ValueProperty, value); }
        }

        public static readonly DependencyProperty ValueProperty =
            DependencyProperty.Register("Value", typeof(string), typeof(DataBox3D), 
                new PropertyMetadata("0", OnValueChanged));

        private static void OnValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            DataBox3D box = d as DataBox3D;
            if (box != null)
            {
                box.ValueText.Text = e.NewValue as string;
            }
        }

        // 单位属性
        public string Unit
        {
            get { return (string)GetValue(UnitProperty); }
            set { SetValue(UnitProperty, value); }
        }

        public static readonly DependencyProperty UnitProperty =
            DependencyProperty.Register("Unit", typeof(string), typeof(DataBox3D), 
                new PropertyMetadata("/单位", OnUnitChanged));

        private static void OnUnitChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            DataBox3D box = d as DataBox3D;
            if (box != null)
            {
                box.UnitText.Text = e.NewValue as string;
            }
        }
    }
}
